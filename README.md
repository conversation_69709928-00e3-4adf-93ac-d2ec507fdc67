# RustyCluster

RustyCluster is a distributed key-value store built in Rust, providing high availability and data consistency through replication. It uses Redis as the underlying storage engine and gRPC for inter-node communication.

## Features

- **Distributed Architecture**: Multiple nodes working together to provide high availability
- **Data Replication**: Automatic replication of data to secondary nodes with configurable consistency levels
- **Site Replication**: Cross-site data replication with automatic failover for disaster recovery
- **Write Consistency**: Synchronous replication to peer Redis nodes with configurable consistency levels (ALL/QUORUM/ONE)
- **Connection Pooling**: Efficient connection management for Redis, inter-node communication, and peer Redis pools
- **Asynchronous Operations**: Support for both synchronous and asynchronous replication modes
- **Rich Data Types**: Comprehensive support for strings, hashes, numeric operations, and Lua scripts
- **TTL Support**: Built-in support for key expiration and time-based operations
- **Client Authentication**: Secure username/password authentication with session management
- **Redis Authentication**: Support for Redis 6.0+ ACL and legacy password authentication
- **Performance Monitoring**: Integration with Tokio Console for real-time profiling and debugging
- **Structured Logging**: Advanced logging with tracing crate for better observability
- **Batch Operations**: Efficient batch processing for high-throughput scenarios
- **Health Monitoring**: Comprehensive health checks for Redis, secondary nodes, and site replication

## Architecture

RustyCluster consists of multiple nodes, where:
- Each node can be either a primary or secondary node
- Primary nodes handle client requests and replicate data to secondary nodes
- Secondary nodes maintain copies of the data and can serve read requests
- All nodes communicate via gRPC
- Redis is used as the underlying storage engine

## Prerequisites

- Rust (latest stable version)
- Redis server
- Protobuf compiler (for gRPC)

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/rustycluster.git
cd rustycluster
```

2. Build the project:
```bash
cargo build --release
```

## Configuration

RustyCluster uses TOML configuration files. A sample configuration file (`config.toml`) looks like this:

```toml
# Redis connection URL for the primary node
# For Redis without authentication:
redis_url = "redis://127.0.0.1:6379"
# For Redis with authentication (replace 'your_password' with your actual password):
# redis_url = "redis://:your_password@127.0.0.1:6379"

# List of secondary node URLs that this node will replicate to
secondary_nodes = ["http://127.0.0.1:50052", "http://127.0.0.1:50053"]

# Port number on which the gRPC server will listen
port = 50051

# Maximum number of connections in the Redis connection pool
redis_pool_size = 512

# Number of secondary nodes to which data should be replicated
replication_factor = 2

# Number of secondary nodes that must have the data before considering it consistent
read_consistency = 0

# If true, replication to secondary nodes happens asynchronously
async_replication = true

# Maximum number of retry attempts when connecting to secondary nodes
max_retries = 3

# Delay in milliseconds between retry attempts
retry_delay_ms = 100

# Maximum age in seconds for a replication batch
replication_batch_max_age_secs = 30

# Maximum number of connections per secondary node in the gRPC client pool
secondary_pool_size = 512

# Write consistency configuration for peer Redis nodes
write_consistency = "QUORUM"  # Options: ALL, QUORUM, ONE
peer_redis_nodes = ["redis://127.0.0.1:6380", "redis://127.0.0.1:6381"]
peer_redis_pool_size = 64

# Site replication configuration
site_replication_enabled = false
site_replication_nodes = [
    { primary = "http://site2-primary:50053", failover = "http://site2-failover:50054" },
    { primary = "http://site3-primary:50053", failover = "http://site3-failover:50054" }
]

# Health check configuration
health_check_enabled = true
health_check_interval_secs = 30
redis_health_check_timeout_secs = 5
secondary_nodes_health_check_timeout_secs = 10

# Performance tuning
use_physical_connections = true  # Use actual TCP connections for better monitoring
batch_flush_interval_ms = 100
max_concurrent_operations = 1000
```

## Client Authentication

RustyCluster supports client authentication with username and password. When enabled, clients must authenticate once during connection initialization before performing any operations.

### Authentication Configuration

Add the following settings to your `config.toml` file:

```toml
# Authentication configuration
auth_enabled = true
auth_username = "your_username"
auth_password = "your_password"
session_duration_secs = 3600  # Session timeout in seconds (1 hour)
```

### Authentication Flow

1. **Connection Initialization**: Client connects to RustyCluster
2. **Authentication**: Client calls the `Authenticate` RPC with username and password
3. **Session Token**: Server returns a session token upon successful authentication
4. **Operations**: Client includes the session token in the `authorization` header for all subsequent operations

### Authentication Notes

- Authentication happens only once during connection initialization
- No authentication is required on each individual operation after the initial authentication
- Session tokens expire after the configured duration (default: 1 hour)
- Authentication is not required between RustyCluster nodes (inter-node communication)
- When `auth_enabled = false`, all requests are allowed without authentication

### Example Usage

1. **Start RustyCluster with authentication enabled**:
```bash
cargo run --release config_auth_test.toml
```

2. **Client Authentication Flow**:
```protobuf
// First, authenticate to get a session token
AuthenticateRequest {
  username: "testuser"
  password: "testpass"
}

// Server responds with:
AuthenticateResponse {
  success: true
  session_token: "uuid-session-token"
  message: "Authentication successful"
}

// Use the session token in subsequent requests
// Add to gRPC metadata: authorization: "Bearer uuid-session-token"
```

3. **All subsequent operations** (Set, Get, Delete, etc.) must include the session token in the authorization header.

### Load Testing with Authentication

For load testing with tools like `ghz`, you have several options:

#### Option 1: Disable Authentication (Recommended for Load Testing)
```bash
# Use config_loadtest.toml with auth_enabled = false
cargo run --release config_loadtest.toml

# Run your existing ghz command
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

#### Option 2: Use ghz with Session Token
```bash
# Get session token
python get_session_token.py --username testuser --password testpass

# Use token with ghz (replace YOUR_SESSION_TOKEN)
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --metadata="authorization:Bearer YOUR_SESSION_TOKEN" --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

#### Option 3: Automated Script
```bash
# Make script executable
chmod +x ghz_with_auth.sh

# Run with authentication
./ghz_with_auth.sh --username testuser --password testpass --rps 10000 -n 200000 -c 1000

# Run without authentication
./ghz_with_auth.sh --no-auth --rps 10000 -n 200000 -c 1000
```

#### Option 4: Windows Batch Script
```cmd
# Get session token (Windows)
get_token.bat --username testuser --password testpass

# Use the displayed token with ghz
ghz.exe --insecure --proto=../rustycluster.proto --call=rustycluster.KeyValueService.Set --data-file=../set-data.json --metadata="authorization:Bearer YOUR_TOKEN" --rps 10000 -n 200000 -c 1000 127.0.0.1:50051
```

📖 **For detailed load testing instructions with authentication, see [LOAD_TEST_WITH_AUTH.md](LOAD_TEST_WITH_AUTH.md)**

### Java Client Example

```java
// Java client authentication example
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;

// Create channel
ManagedChannel channel = ManagedChannelBuilder.forAddress("localhost", 50051)
    .usePlaintext()
    .build();

KeyValueServiceGrpc.KeyValueServiceBlockingStub stub =
    KeyValueServiceGrpc.newBlockingStub(channel);

// Authenticate
AuthenticateRequest authRequest = AuthenticateRequest.newBuilder()
    .setUsername("testuser")
    .setPassword("testpass")
    .build();

AuthenticateResponse authResponse = stub.authenticate(authRequest);

if (authResponse.getSuccess()) {
    String sessionToken = authResponse.getSessionToken();

    // Create metadata with session token
    Metadata metadata = new Metadata();
    Metadata.Key<String> authKey = Metadata.Key.of("authorization",
        Metadata.ASCII_STRING_MARSHALLER);
    metadata.put(authKey, "Bearer " + sessionToken);

    // Use authenticated stub for operations
    KeyValueServiceGrpc.KeyValueServiceBlockingStub authenticatedStub =
        MetadataUtils.attachHeaders(stub, metadata);

    // Now you can use authenticatedStub for all operations
    PingResponse pingResponse = authenticatedStub.ping(PingRequest.newBuilder().build());
}
```

## Redis Authentication

RustyCluster supports Redis servers with various authentication methods. Configure your Redis connection in the `config.toml` file using one of the following formats:

### Authentication Options

1. **Username and Password Authentication** (recommended for Redis 6.0+):
```toml
# For Redis with both username and password
redis_url = "redis://username:password@127.0.0.1:6379"
```

2. **Password-only Authentication**:
```toml
# For Redis with password only (no username)
redis_url = "redis://:password@127.0.0.1:6379"
```

3. **Username-only Authentication**:
```toml
# For Redis with username only (treated as password)
redis_url = "redis://username@127.0.0.1:6379"
```

4. **No Authentication**:
```toml
# For Redis without authentication
redis_url = "redis://127.0.0.1:6379"
```

### Authentication Notes

- Redis 6.0+ supports ACL with username and password authentication
- For Redis versions before 6.0, use the password-only format
- When using username-only format (`redis://username@host:port`), Redis treats the username as a password
- For security, RustyCluster logs Redis URLs without exposing credentials

## Running the Application

1. Start Redis servers for each node:
```bash
# Without authentication
redis-server --port 6379  # For primary node
redis-server --port 6370  # For secondary node 1
redis-server --port 6371  # For secondary node 2

# With authentication (example)
redis-server --port 6379 --requirepass "your_password"  # For primary node
redis-server --port 6370 --requirepass "your_password"  # For secondary node 1
redis-server --port 6371 --requirepass "your_password"  # For secondary node 2
```

2. Start the nodes with their respective configuration files:
```bash
# Start primary node
cargo run --release config.toml

# Start secondary nodes
cargo run --release config_node2.toml
cargo run --release config_node3.toml
```

## API Reference

### Authentication Operations
- `Authenticate(username, password)`: Authenticate client and receive session token

### String Operations
- `Set(key, value)`: Set a key-value pair
- `Get(key)`: Retrieve a value by key
- `Delete(key)`: Delete a key
- `SetEx(key, value, ttl)`: Set a key-value pair with expiration
- `SetExpiry(key, ttl)`: Set expiration for an existing key
- `SetNX(key, value)`: Set key only if it does not exist
- `Exists(key)`: Check if a key exists

### Numeric Operations
- `IncrBy(key, value)`: Increment a numeric value
- `DecrBy(key, value)`: Decrement a numeric value
- `IncrByFloat(key, value)`: Increment a floating-point value

### Hash Operations
- `HSet(key, field, value)`: Set a field in a hash
- `HGet(key, field)`: Get a field from a hash
- `HGetAll(key)`: Get all fields from a hash
- `HMSet(key, fields)`: Set multiple hash fields and values in a single operation
- `HExists(key, field)`: Check if a hash field exists
- `HIncrBy(key, field, value)`: Increment a numeric field
- `HDecrBy(key, field, value)`: Decrement a numeric field
- `HIncrByFloat(key, field, value)`: Increment a floating-point field

### Script Operations
- `LoadScript(script)`: Load a Lua script and return its SHA hash
- `EvalSha(sha, keys, args)`: Execute a Lua script by its SHA hash

### Batch Operations
- `BatchWrite(operations)`: Execute multiple operations in a single batch for improved performance

### Health Check Operations
- `Ping()`: Check server connectivity and health status

## Recent Upgrades and Improvements

### Version 2.2.3 - Major Feature Enhancements

#### 🆕 **New Redis Operations**
- **HMSET**: Set multiple hash fields and values in a single atomic operation
- **SETNX**: Set key only if it does not exist (conditional set)
- **EXISTS**: Check if a key exists in the database
- **HEXISTS**: Check if a specific field exists in a hash
- **LOAD_SCRIPT**: Load Lua scripts and return SHA hash for execution
- **EVALSHA**: Execute Lua scripts by SHA hash with keys and arguments

#### 🔄 **Enhanced Replication System**
- **Full Replication Support**: All new operations support both local node replication and site replication
- **Improved Encoding**: Enhanced data encoding/decoding for complex operations (HMSET, EVALSHA)
- **Batch Processing**: All operations integrated with efficient batch processing system
- **Consistency Guarantees**: Maintains ACID properties across all replication scenarios

#### ⚡ **Performance Improvements**
- **Optimized Connection Pooling**: Enhanced Redis and gRPC connection management
- **Async Processing**: Improved asynchronous operation handling with tokio::spawn
- **Batch Optimization**: More efficient batch processing for high-throughput scenarios
- **Memory Management**: Reduced memory allocations and improved garbage collection

#### 🔧 **Infrastructure Upgrades**
- **Redis 0.31**: Upgraded from Redis 0.24 to 0.31 for better performance and features
- **Deadpool-Redis 0.20**: Upgraded from 0.14 to 0.20 for improved connection pooling
- **Tracing Integration**: Migrated from log4rs to tracing crate for better performance
- **Protocol Buffer Updates**: Enhanced protobuf definitions with new operation types

#### 🛡️ **Security and Reliability**
- **Enhanced Authentication**: Improved session management and token validation
- **Error Handling**: Better error propagation and recovery mechanisms
- **Health Monitoring**: Comprehensive health checks for all system components
- **Logging Improvements**: Structured logging with configurable patterns and file rotation

#### 📊 **Monitoring and Observability**
- **Tokio Console Support**: Real-time profiling and debugging capabilities
- **Performance Metrics**: Enhanced monitoring of RPS, latency, and throughput
- **Structured Logging**: Better log organization with contextual information
- **Health Dashboards**: Comprehensive system health monitoring

### Compatibility Notes
- **Backward Compatible**: All existing operations continue to work without changes
- **Configuration Updates**: New optional configuration parameters for enhanced features
- **Client Libraries**: Existing client code remains compatible with new server version

## Usage Examples

### New Redis Operations Examples

#### HMSET - Set Multiple Hash Fields
```bash
# Using grpcurl to set multiple hash fields at once
grpcurl -plaintext -d '{
  "key": "user:1001",
  "fields": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": "30",
    "city": "New York"
  }
}' localhost:50051 rustycluster.KeyValueService.HMSet
```

#### SETNX - Conditional Set
```bash
# Set key only if it doesn't exist
grpcurl -plaintext -d '{
  "key": "lock:resource1",
  "value": "locked_by_process_123"
}' localhost:50051 rustycluster.KeyValueService.SetNX
```

#### EXISTS and HEXISTS - Check Existence
```bash
# Check if key exists
grpcurl -plaintext -d '{
  "key": "user:1001"
}' localhost:50051 rustycluster.KeyValueService.Exists

# Check if hash field exists
grpcurl -plaintext -d '{
  "key": "user:1001",
  "field": "email"
}' localhost:50051 rustycluster.KeyValueService.HExists
```

#### Lua Script Operations
```bash
# Load a Lua script
grpcurl -plaintext -d '{
  "script": "return redis.call('\''get'\'', KEYS[1]) or '\''default'\''"
}' localhost:50051 rustycluster.KeyValueService.LoadScript

# Execute script by SHA (replace with actual SHA from LoadScript response)
grpcurl -plaintext -d '{
  "sha": "your_script_sha_here",
  "keys": ["mykey"],
  "args": []
}' localhost:50051 rustycluster.KeyValueService.EvalSha
```

#### Batch Operations
```bash
# Execute multiple operations in a single batch
grpcurl -plaintext -d '{
  "operations": [
    {
      "operation_type": 0,
      "key": "key1",
      "value": "value1"
    },
    {
      "operation_type": 11,
      "key": "hash1",
      "hash_fields": {
        "field1": "value1",
        "field2": "value2"
      }
    }
  ]
}' localhost:50051 rustycluster.KeyValueService.BatchWrite
```

## Performance Considerations

### Connection Management
- **Multi-tier Connection Pooling**: Separate pools for Redis, secondary nodes, peer Redis nodes, and site replication
- **Physical Connections**: Option to use actual TCP connections instead of HTTP/2 multiplexing for better monitoring
- **Configurable Pool Sizes**: Fine-tuned pool sizes for different connection types based on workload requirements
- **Connection Health Monitoring**: Automatic health checks and connection recovery

### Replication Performance
- **Asynchronous Replication**: Non-blocking replication to secondary nodes for improved client response times
- **Batch Processing**: Intelligent batching of operations for efficient network utilization
- **Parallel Processing**: Concurrent replication to multiple nodes with controlled concurrency limits
- **Site Replication Optimization**: Efficient cross-site replication with failover support

### Operation Efficiency
- **Batch Operations**: Single-request multiple operations for reduced network overhead
- **Memory Optimization**: Reduced allocations and improved garbage collection patterns
- **Async Processing**: Tokio-based async runtime for maximum concurrency
- **Lock-free Operations**: Minimized contention in high-throughput scenarios

### Performance Metrics
- **Target Performance**: 10,000+ RPS with replication enabled
- **Latency Optimization**: Sub-millisecond operation latencies under normal load
- **Scalability**: Linear scaling with additional nodes and Redis instances
- **Resource Efficiency**: Optimized CPU and memory usage patterns

### Monitoring and Tuning
- **Real-time Metrics**: Integration with Tokio Console for live performance monitoring
- **Structured Logging**: Performance-optimized logging with minimal overhead
- **Health Dashboards**: Comprehensive monitoring of all system components
- **Configurable Thresholds**: Adjustable performance parameters for different environments

## Error Handling

- Automatic retries for failed operations
- Configurable retry count and delay
- Detailed error logging
- Graceful handling of node failures

## Monitoring and Tracing

RustyCluster uses the `tracing` crate for structured logging and diagnostics. This provides several benefits:

- **Structured logging**: Better organization of log data with contextual information
- **Spans**: Track operations across asynchronous boundaries
- **Performance**: More efficient logging with less overhead
- **Integration with Tokio**: Better integration with the Tokio ecosystem

### Configuration

Tracing is configured using a TOML file (default: `logconfig.toml`):

```toml
# Tracing configuration file for RustyCluster
file = "rustycluster.log"
max_size = 10485760  # 10MB
max_files = 5
level = "info"
pattern = "{d(%Y-%m-%d %H:%M:%S%.3f)} - {l} - {M} - {m}\n"
console_enabled = false
```

### Environment Variables

You can override the log level using the `RUST_LOG` environment variable:

```bash
RUST_LOG=debug cargo run --release config.toml
```

### Tokio Console Integration

RustyCluster supports [Tokio Console](https://github.com/tokio-rs/console) for real-time profiling and debugging. To enable it:

1. Build with the console feature:
```bash
cargo build --release --features console
```

2. Set `console_enabled = true` in your `logconfig.toml` file

3. Run with the `tokio_unstable` flag:
```bash
RUSTFLAGS="--cfg tokio_unstable" cargo run --release --features console config.toml
```

4. In another terminal, run the Tokio Console:
```bash
cargo install --locked tokio-console
tokio-console
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.